'use client';

import { Typography, Box, CircularProgress, useTheme } from '@mui/material';
import { LineChart } from '@mui/x-charts/LineChart';
import { useState, useEffect } from 'react';
import PanelHeader from './PanelHeader';
import PanelSubheader from './PanelSubheader';
import DashboardPanel from './DashboardPanel';

interface SeparateWaveHistoryPanelProps {
	focusType: 'agency' | 'brand' | 'region' | 'wave';
	focusValue: string;
}

interface ChartDataPoint {
	wave: string;
	score: number | null;
	responseCount: number;
	[key: string]: string | number | null | undefined;
}

interface WaveHistoryData {
	waveNames: string[];
	abiOnAgency: {
		scores: (number | null)[];
		npsScores: (number | null)[];
		responseCounts: number[];
	};
	agencyOnAbi: {
		scores: (number | null)[];
		npsScores: (number | null)[];
		responseCounts: number[];
	};
	responseCount: number;
}

export default function SeparateWaveHistoryPanel({
	focusType,
	focusValue,
}: SeparateWaveHistoryPanelProps) {
	const [data, setData] = useState<WaveHistoryData | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const theme = useTheme();

	useEffect(() => {
		const fetchWaveHistory = async () => {
			try {
				setLoading(true);
				setError(null);

				const params = new URLSearchParams({
					focusType,
					focusValue,
				});

				const response = await fetch(
					`/api/responses/wave-history?${params.toString()}`
				);

				if (!response.ok) {
					throw new Error('Failed to fetch wave history');
				}

				const responseData = await response.json();
				setData(responseData);
			} catch (err) {
				console.error('Error fetching wave history:', err);
				setError('Failed to load wave history');
				setData(null);
			} finally {
				setLoading(false);
			}
		};

		fetchWaveHistory();
	}, [focusType, focusValue]);

	// Transform data for charts
	const transformDataForChart = (scores: (number | null)[], responseCounts: number[], waveNames: string[]): ChartDataPoint[] => {
		return waveNames.map((wave, index) => ({
			wave,
			score: scores[index],
			responseCount: responseCounts[index] || 0,
		})).filter(point => point.score !== null); // Only include waves with data
	};

	const renderChart = (
		title: string,
		chartData: ChartDataPoint[],
		color: string,
		dataKey: string = 'score'
	) => {
		if (chartData.length === 0) {
			return (
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
						border: '1px solid rgba(0, 0, 0, 0.05)',
						borderRadius: 2,
						bgcolor: 'rgba(0, 0, 0, 0.02)',
					}}
				>
					<Typography variant="body2" color="text.secondary" textAlign="center">
						No data available for {title}
					</Typography>
				</Box>
			);
		}

		return (
			<Box
				sx={{
					width: '100%',
					position: 'relative',
					textAlign: 'center',
				}}
			>
				<LineChart
					dataset={chartData}
					xAxis={[
						{
							scaleType: 'point',
							dataKey: 'wave',
							tickLabelStyle: {
								display: 'none',
								fontSize: 10,
								fill: theme.palette.text.secondary,
							},
						},
					]}
					yAxis={[
						{
							min: 1,
							max: 5,
							tickNumber: 5,
							tickLabelStyle: {
								display: 'none',
								fontSize: 10,
								fill: theme.palette.text.secondary,
							},
						},
					]}
					series={[
						{
							dataKey: dataKey,
							label: title,
							color: color,
							curve: 'natural' as const,
							showMark: true,
							area: false,
							valueFormatter: (value: number | null, context) => {
								if (value === null) return '';
								const dataIndex = context?.dataIndex;
								if (typeof dataIndex === 'number' && chartData[dataIndex]) {
									const responseCount = chartData[dataIndex].responseCount;
									return `${value} (${responseCount} responses)`;
								}
								return value?.toString() || '';
							},
						},
					]}
					height={200}
					margin={{ left: 30, right: 20, top: 30, bottom: 30 }}
					slots={{
						mark: ({ x, y, dataIndex }) => {
							if (
								typeof x !== 'number' ||
								typeof y !== 'number' ||
								typeof dataIndex !== 'number'
							) {
								return null;
							}
							const value = chartData[dataIndex]?.score;

							return (
								<g key={`${color}-${dataIndex}`}>
									<circle
										cx={x}
										cy={y}
										r={4}
										fill={`${color}80`}
										stroke={color}
										strokeWidth={2}
										style={{
											filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.2))',
										}}
									/>
									<text
										x={x}
										y={y - 12}
										textAnchor="middle"
										fontSize={11}
										fontWeight="bold"
										fill={color}
									>
										{value}
									</text>
								</g>
							);
						},
					}}
					sx={{
						border: '1px solid rgba(0, 0, 0, 0.05)',
						borderRadius: 2,
						'& .MuiChartsAxis-tick': {
							stroke: 'rgba(0, 0, 0, 0.1)',
						},
						'& .MuiChartsAxis-line': {
							stroke: 'rgba(0, 0, 0, 0.1)',
						},
						'& .MuiLineElement-root': {
							strokeWidth: 3,
						},
					}}
				/>
			</Box>
		);
	};

	// If no data, show a message
	if (!loading && (!data || (data.abiOnAgency.scores.every(s => s === null) && data.agencyOnAbi.scores.every(s => s === null)))) {
		return (
			<DashboardPanel sx={{ p: 2, bgcolor: '#e3f2fd', height: 300 }}>
				<PanelHeader gutterBottom>{focusValue} Wave History</PanelHeader>
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<Typography variant="body2" color="text.secondary" textAlign="center">
						No wave history data available for {focusValue}.
					</Typography>
				</Box>
			</DashboardPanel>
		);
	}

	return (
		<DashboardPanel sx={{ p: 2, textAlign: 'center' }}>
			<PanelHeader>{focusValue} Wave History</PanelHeader>

			{loading ? (
				<Box
					sx={{
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<CircularProgress size={24} />
				</Box>
			) : (
				<Box
					sx={{
						display: 'flex',
						flexDirection: { xs: 'column', md: 'row' },
						gap: 3,
						alignItems: 'flex-start',
					}}
				>
					<Box sx={{ flex: 1 }}>
						<PanelSubheader gutterBottom sx={{ mb: 1 }}>
							ABI → Agency
						</PanelSubheader>
						{data && renderChart(
							'ABI → Agency',
							transformDataForChart(data.abiOnAgency.scores, data.abiOnAgency.responseCounts, data.waveNames),
							theme.palette.primary.main
						)}
					</Box>
					<Box sx={{ flex: 1 }}>
						<PanelSubheader gutterBottom sx={{ mb: 1 }}>
							Agency → ABI
						</PanelSubheader>
						{data && renderChart(
							'Agency → ABI',
							transformDataForChart(data.agencyOnAbi.scores, data.agencyOnAbi.responseCounts, data.waveNames),
							theme.palette.secondary.main
						)}
					</Box>
				</Box>
			)}

			{error && (
				<Typography variant="body2" color="error" sx={{ mt: 1 }}>
					{error}
				</Typography>
			)}
		</DashboardPanel>
	);
}
